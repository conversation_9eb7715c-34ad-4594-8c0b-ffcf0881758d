version: '3.8'

services:
  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: taskshare-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=file:./dev.db
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - PORT=3001
      - FRONTEND_URL=http://localhost:5173
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_db:/app/prisma
    networks:
      - taskshare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: taskshare-frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost:3001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - taskshare-network
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Named volumes for data persistence
volumes:
  backend_db:
    driver: local

# Custom network for service communication
networks:
  taskshare-network:
    driver: bridge
