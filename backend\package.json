{"name": "taskshare-backend", "version": "1.0.0", "description": "Backend API for TaskShare application", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@prisma/client": "^5.7.1", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "jest": "^30.0.5", "prisma": "^5.7.1", "ts-jest": "^29.4.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}}