{"name": "taskshare", "version": "1.0.0", "description": "Collaborative task management application", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["task", "management", "collaboration"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}