// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())

  // Relations
  ownedLists TaskList[] @relation("ListOwner")
  sharedLists ListShare[]
  comments   Comment[]

  @@map("users")
}

model TaskList {
  id        String   @id @default(cuid())
  title     String
  ownerId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  owner  User        @relation("ListOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  tasks  Task[]
  shares ListShare[]

  @@map("task_lists")
}

model Task {
  id        String   @id @default(cuid())
  title     String
  completed Boolean  @default(false)
  order     Int      @default(0)
  listId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  list     TaskList  @relation(fields: [listId], references: [id], onDelete: Cascade)
  comments Comment[]

  @@map("tasks")
}

model ListShare {
  id        String   @id @default(cuid())
  listId    String
  userId    String
  createdAt DateTime @default(now())

  // Relations
  list TaskList @relation(fields: [listId], references: [id], onDelete: Cascade)
  user User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([listId, userId])
  @@map("list_shares")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  taskId    String
  userId    String
  createdAt DateTime @default(now())

  // Relations
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("comments")
}